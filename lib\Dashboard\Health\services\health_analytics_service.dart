import '../models/health_record_isar.dart';
import '../models/medication_isar.dart';
import '../models/treatment_isar.dart';
import '../models/vaccination_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';

/// Data class to hold all health analytics results
class HealthAnalyticsResult {
  // KPI Metrics
  final int totalHealthRecords;
  final int activeRecords;
  final int resolvedRecords;
  final int treatmentRecords;
  final int vaccinationRecords;
  final int medicationRecords;
  final double averageHealthScore;
  final double totalHealthCosts;

  // Distributions
  final Map<String, int> recordTypeDistribution;
  final Map<String, int> statusDistribution;
  final Map<String, int> conditionDistribution;
  final Map<String, int> treatmentDistribution;

  // Health insights
  final int cattleWithRecentTreatments;
  final int overdueVaccinations;
  final List<String> chronicConditions;
  final double treatmentSuccessRate;

  // Financial metrics
  final double averageTreatmentCost;
  final double totalVaccinationCosts;
  final double totalMedicationCosts;

  const HealthAnalyticsResult({
    required this.totalHealthRecords,
    required this.activeRecords,
    required this.resolvedRecords,
    required this.treatmentRecords,
    required this.vaccinationRecords,
    required this.medicationRecords,
    required this.averageHealthScore,
    required this.totalHealthCosts,
    required this.recordTypeDistribution,
    required this.statusDistribution,
    required this.conditionDistribution,
    required this.treatmentDistribution,
    required this.cattleWithRecentTreatments,
    required this.overdueVaccinations,
    required this.chronicConditions,
    required this.treatmentSuccessRate,
    required this.averageTreatmentCost,
    required this.totalVaccinationCosts,
    required this.totalMedicationCosts,
  });

  /// Empty result for when there's no data
  static const empty = HealthAnalyticsResult(
    totalHealthRecords: 0,
    activeRecords: 0,
    resolvedRecords: 0,
    treatmentRecords: 0,
    vaccinationRecords: 0,
    medicationRecords: 0,
    averageHealthScore: 0.0,
    totalHealthCosts: 0.0,
    recordTypeDistribution: {},
    statusDistribution: {},
    conditionDistribution: {},
    treatmentDistribution: {},
    cattleWithRecentTreatments: 0,
    overdueVaccinations: 0,
    chronicConditions: [],
    treatmentSuccessRate: 0.0,
    averageTreatmentCost: 0.0,
    totalVaccinationCosts: 0.0,
    totalMedicationCosts: 0.0,
  );
}

/// Pure analytics service for health calculations - no state, just calculations
/// Following the cattle module pattern with single-pass data processing
class HealthAnalyticsService {

  /// Main calculation method - single entry point with O(n) efficiency
  static HealthAnalyticsResult calculate(
    List<HealthRecordIsar> healthRecords,
    List<MedicationIsar> medications,
    List<TreatmentIsar> treatments,
    List<VaccinationIsar> vaccinations,
    List<CattleIsar> cattle,
  ) {
    if (healthRecords.isEmpty && medications.isEmpty && treatments.isEmpty && vaccinations.isEmpty) {
      return HealthAnalyticsResult.empty;
    }

    // Single pass through the data for maximum efficiency
    final accumulator = _HealthAnalyticsAccumulator();

    // Process all health data types
    for (final record in healthRecords) {
      accumulator.processHealthRecord(record);
    }

    for (final medication in medications) {
      accumulator.processMedication(medication);
    }

    for (final treatment in treatments) {
      accumulator.processTreatment(treatment);
    }

    for (final vaccination in vaccinations) {
      accumulator.processVaccination(vaccination);
    }

    // Calculate cattle-specific metrics
    accumulator.calculateCattleMetrics(cattle, healthRecords, vaccinations);

    return accumulator.toResult();
  }

  /// Calculate health score for individual cattle
  static double calculateCattleHealthScore(
    String cattleId,
    List<HealthRecordIsar> healthRecords,
    List<TreatmentIsar> treatments,
    List<VaccinationIsar> vaccinations,
  ) {
    const baseScore = 100.0;
    double score = baseScore;

    // Filter records for this cattle
    final cattleHealthRecords = healthRecords.where((r) => r.cattleId == cattleId).toList();
    final cattleTreatments = treatments.where((t) => t.cattleId == cattleId).toList();
    final cattleVaccinations = vaccinations.where((v) => v.cattleId == cattleId).toList();

    // Deduct points for recent treatments (indicates health issues)
    final recentTreatments = cattleTreatments.where((t) {
      if (t.date == null) return false;
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
      return t.date!.isAfter(thirtyDaysAgo);
    }).length;
    score -= recentTreatments * 5.0; // 5 points per recent treatment

    // Deduct points for unresolved health records
    final unresolvedRecords = cattleHealthRecords.where((r) => r.isResolved != true).length;
    score -= unresolvedRecords * 10.0; // 10 points per unresolved issue

    // Add points for up-to-date vaccinations
    final recentVaccinations = cattleVaccinations.where((v) {
      if (v.date == null) return false;
      final oneYearAgo = DateTime.now().subtract(const Duration(days: 365));
      return v.date!.isAfter(oneYearAgo);
    }).length;
    score += recentVaccinations * 2.0; // 2 points per recent vaccination

    // Ensure score stays within bounds
    return score.clamp(0.0, 100.0);
  }

  /// Identify chronic conditions (conditions appearing multiple times)
  static List<String> identifyChronicConditions(
    List<HealthRecordIsar> healthRecords,
    {int threshold = 3}
  ) {
    final conditionCounts = <String, int>{};

    for (final record in healthRecords) {
      final condition = record.condition?.toLowerCase().trim();
      if (condition != null && condition.isNotEmpty) {
        conditionCounts[condition] = (conditionCounts[condition] ?? 0) + 1;
      }
    }

    return _getChronicConditionsFromCounts(conditionCounts, threshold);
  }

  /// Extract chronic conditions from condition counts - reusable helper
  static List<String> _getChronicConditionsFromCounts(
    Map<String, int> conditionCounts,
    int threshold
  ) {
    return conditionCounts.entries
        .where((entry) => entry.value >= threshold)
        .map((entry) => entry.key)
        .toList();
  }
}

/// Efficient single-pass accumulator for all health analytics calculations
class _HealthAnalyticsAccumulator {
  // Constants for calculations
  static const int _recentTreatmentDays = 30;
  static const int _chronicConditionThreshold = 3;

  // Basic counts
  int totalHealthRecords = 0;
  int activeRecords = 0;
  int resolvedRecords = 0;
  int treatmentRecords = 0;
  int vaccinationRecords = 0;
  int medicationRecords = 0;

  // Financial metrics
  double totalHealthCosts = 0.0;
  double totalVaccinationCosts = 0.0;
  double totalMedicationCosts = 0.0;
  int recordsWithCost = 0;

  // Distributions
  final Map<String, int> recordTypeDistribution = {};
  final Map<String, int> statusDistribution = {};
  final Map<String, int> conditionDistribution = {};
  final Map<String, int> treatmentDistribution = {};

  // Health insights
  final Set<String> cattleWithRecentTreatments = {};
  int overdueVaccinations = 0;
  final Map<String, int> conditionCounts = {};

  /// Process a single health record
  void processHealthRecord(HealthRecordIsar record) {
    totalHealthRecords++;

    // Status processing
    final status = record.status?.toLowerCase() ?? 'unknown';
    if (status == 'active') {
      activeRecords++;
    } else if (status == 'resolved' || record.isResolved == true) {
      resolvedRecords++;
    }

    // Record type processing
    final recordType = record.recordType ?? 'general';
    recordTypeDistribution[recordType] = (recordTypeDistribution[recordType] ?? 0) + 1;
    statusDistribution[status] = (statusDistribution[status] ?? 0) + 1;

    if (recordType.toLowerCase() == 'treatment') {
      treatmentRecords++;
    } else if (recordType.toLowerCase() == 'vaccination') {
      vaccinationRecords++;
    }

    // Condition processing
    final condition = record.condition?.toLowerCase().trim();
    if (condition != null && condition.isNotEmpty) {
      conditionDistribution[condition] = (conditionDistribution[condition] ?? 0) + 1;
      conditionCounts[condition] = (conditionCounts[condition] ?? 0) + 1;
    }

    // Treatment processing
    final treatment = record.treatment?.toLowerCase().trim();
    if (treatment != null && treatment.isNotEmpty) {
      treatmentDistribution[treatment] = (treatmentDistribution[treatment] ?? 0) + 1;
    }

    // Financial processing
    final cost = record.cost ?? 0.0;
    if (cost > 0) {
      totalHealthCosts += cost;
      recordsWithCost++;
    }

    // Recent treatment tracking
    if (record.date != null && record.cattleId != null) {
      final recentDate = DateTime.now().subtract(const Duration(days: _recentTreatmentDays));
      if (record.date!.isAfter(recentDate)) {
        cattleWithRecentTreatments.add(record.cattleId!);
      }
    }
  }

  /// Process a single medication record
  void processMedication(MedicationIsar medication) {
    medicationRecords++;

    final cost = medication.cost ?? 0.0;
    if (cost > 0) {
      totalMedicationCosts += cost;
      totalHealthCosts += cost;
    }
  }

  /// Process a single treatment record
  void processTreatment(TreatmentIsar treatment) {
    // Note: TreatmentIsar might overlap with HealthRecordIsar
    // Only count if not already counted in health records
    final cost = double.tryParse(treatment.cost ?? '0') ?? 0.0;
    if (cost > 0) {
      totalHealthCosts += cost;
    }
  }

  /// Process a single vaccination record
  void processVaccination(VaccinationIsar vaccination) {
    vaccinationRecords++;

    final cost = vaccination.cost ?? 0.0;
    if (cost > 0) {
      totalVaccinationCosts += cost;
      totalHealthCosts += cost;
    }

    // Check if vaccination is overdue (next due date passed)
    if (vaccination.nextDueDate != null && vaccination.nextDueDate!.isBefore(DateTime.now())) {
      overdueVaccinations++;
    }
  }

  /// Calculate cattle-specific metrics
  void calculateCattleMetrics(
    List<CattleIsar> cattle,
    List<HealthRecordIsar> healthRecords,
    List<VaccinationIsar> vaccinations,
  ) {
    // This method can be expanded for more complex cattle-specific calculations
    // Currently, most cattle metrics are calculated in the process methods
  }

  /// Convert accumulated data to immutable result
  HealthAnalyticsResult toResult() {
    final averageHealthScore = _calculateAverageHealthScore();
    final treatmentSuccessRate = _calculateTreatmentSuccessRate();
    final averageTreatmentCost = recordsWithCost > 0 ? totalHealthCosts / recordsWithCost : 0.0;

    // Reuse the static helper method instead of duplicating logic
    final chronicConditions = _getChronicConditionsFromCounts(conditionCounts, _chronicConditionThreshold);

    return HealthAnalyticsResult(
      totalHealthRecords: totalHealthRecords,
      activeRecords: activeRecords,
      resolvedRecords: resolvedRecords,
      treatmentRecords: treatmentRecords,
      vaccinationRecords: vaccinationRecords,
      medicationRecords: medicationRecords,
      averageHealthScore: averageHealthScore,
      totalHealthCosts: totalHealthCosts,
      recordTypeDistribution: Map.unmodifiable(recordTypeDistribution),
      statusDistribution: Map.unmodifiable(statusDistribution),
      conditionDistribution: Map.unmodifiable(conditionDistribution),
      treatmentDistribution: Map.unmodifiable(treatmentDistribution),
      cattleWithRecentTreatments: cattleWithRecentTreatments.length,
      overdueVaccinations: overdueVaccinations,
      chronicConditions: chronicConditions,
      treatmentSuccessRate: treatmentSuccessRate,
      averageTreatmentCost: averageTreatmentCost,
      totalVaccinationCosts: totalVaccinationCosts,
      totalMedicationCosts: totalMedicationCosts,
    );
  }

  /// Calculate average health score across all cattle
  double _calculateAverageHealthScore() {
    // This is a simplified calculation
    // In a real implementation, you'd calculate individual cattle scores and average them
    if (totalHealthRecords == 0) return 85.0; // Default good health score

    final resolvedPercentage = resolvedRecords / totalHealthRecords;
    return (resolvedPercentage * 100).clamp(0.0, 100.0);
  }

  /// Calculate treatment success rate
  double _calculateTreatmentSuccessRate() {
    if (treatmentRecords == 0) return 100.0; // No treatments = perfect success rate

    final successfulTreatments = resolvedRecords;
    return (successfulTreatments / treatmentRecords * 100).clamp(0.0, 100.0);
  }

  /// Helper method to get chronic conditions from condition counts
  List<String> _getChronicConditionsFromCounts(Map<String, int> conditionCounts, int threshold) {
    return conditionCounts.entries
        .where((entry) => entry.value >= threshold)
        .map((entry) => entry.key)
        .toList();
  }
}
