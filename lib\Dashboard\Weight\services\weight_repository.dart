import 'package:isar/isar.dart';
import '../models/weight_record_isar.dart';
import '../../../services/database/isar_service.dart';

/// Pure reactive repository for Weight module database operations
/// Following the cattle module pattern: Stream-only, logic-free, simple CRUD
/// Maximum purity: no error handling, no logging - exceptions bubble up naturally
class WeightRepository {
  final IsarService _isarService;

  // Public constructor with explicit dependency injection
  WeightRepository(this._isarService);

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  //=== REACTIVE WEIGHT RECORDS STREAMS ===//

  /// Watches all weight records with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<WeightRecordIsar>> watchAllWeightRecords() {
    return _isar.weightRecordIsars.where().watch(fireImmediately: true);
  }

  //=== WEIGHT RECORDS CRUD ===//

  /// Save (add or update) a weight record using <PERSON><PERSON>'s native upsert
  Future<void> saveRecord(WeightRecordIsar record) async {
    await _isar.writeTxn(() async {
      await _isar.weightRecordIsars.put(record);
      await record.cattle.save(); // Persist the IsarLink
    });
  }

  /// Delete a weight record by its Isar ID
  Future<void> deleteRecord(int recordId) async {
    await _isar.writeTxn(() async {
      await _isar.weightRecordIsars.delete(recordId);
    });
  }

  //=== REACTIVE WEIGHT GOALS STREAMS ===//

  /// Watches all weight goals with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<WeightGoalIsar>> watchAllWeightGoals() {
    return _isar.weightGoalIsars.where().watch(fireImmediately: true);
  }

  //=== WEIGHT GOALS CRUD ===//

  /// Save (add or update) a weight goal using Isar's native upsert
  Future<void> saveGoal(WeightGoalIsar goal) async {
    await _isar.writeTxn(() async {
      await _isar.weightGoalIsars.put(goal);
      await goal.cattle.save(); // Persist the IsarLink
    });
  }

  /// Delete a weight goal by its Isar ID
  Future<void> deleteGoal(int goalId) async {
    await _isar.writeTxn(() async {
      await _isar.weightGoalIsars.delete(goalId);
    });
  }

  //=== BACKWARD COMPATIBILITY METHODS ===//

  /// Add record - alias for saveRecord for backward compatibility
  Future<void> addRecord(WeightRecordIsar record) async {
    await saveRecord(record);
  }

  /// Update record - alias for saveRecord for backward compatibility
  Future<void> updateRecord(WeightRecordIsar record) async {
    await saveRecord(record);
  }

  /// Watch weights for specific cattle (for form dialog)
  /// Returns a Stream<List> for reactive updates
  Stream<List<WeightRecordIsar>> watchWeightsForCattle(String cattleBusinessId) {
    return _isar.weightRecordIsars
        .filter()
        .cattle((q) => q.businessIdEqualTo(cattleBusinessId))
        .watch(fireImmediately: true);
  }
}
