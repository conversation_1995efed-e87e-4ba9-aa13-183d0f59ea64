import 'package:flutter/material.dart';
import '../controllers/events_controller.dart';
import '../../../constants/app_colors.dart';

/// Events Insights Tab - Shows insights and recommendations for events
/// Follows the cattle insights tab pattern exactly
class EventsInsightsTab extends StatefulWidget {
  final EventsController controller;

  const EventsInsightsTab({
    super.key,
    required this.controller,
  });

  @override
  State<EventsInsightsTab> createState() => _EventsInsightsTabState();
}

class _EventsInsightsTabState extends State<EventsInsightsTab> {
  @override
  Widget build(BuildContext context) {
    // Pure dependency injection - all dependencies provided via constructor
    return ListenableBuilder(
      listenable: widget.controller,
      builder: (context, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              _buildHeader(context),
              const SizedBox(height: 24),

              // Event Insights
              _buildEventInsights(context),
              const SizedBox(height: 24),

              // Management Recommendations
              _buildManagementRecommendations(context),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.eventsColor,
            AppColors.eventsColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.eventsColor.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb,
                color: Colors.white,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                'Event Management Insights',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              _buildHeaderStat('Total Events', widget.controller.totalEvents.toString(), Icons.event),
              const SizedBox(width: 24),
              _buildHeaderStat('Upcoming', widget.controller.upcomingEvents.toString(), Icons.schedule),
              const SizedBox(width: 24),
              _buildHeaderStat('Completed', widget.controller.completedEvents.toString(), Icons.check_circle),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderStat(String label, String value, IconData icon) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Icon(icon, color: Colors.white, size: 20),
            const SizedBox(height: 4),
            Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEventInsights(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Event Management Insights',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        if (widget.controller.totalEvents == 0)
          _buildEmptyInsightsCard(context)
        else
          ..._buildInsightCards(context),
      ],
    );
  }

  List<Widget> _buildInsightCards(BuildContext context) {
    final insights = <Widget>[];
    
    // Completion rate insight
    final completionRate = widget.controller.completionRate;
    if (completionRate < 0.8) {
      insights.add(_buildInsightCard(
        context,
        'Low Completion Rate',
        'Your event completion rate is ${(completionRate * 100).toStringAsFixed(1)}%. Consider setting reminders.',
        Icons.warning,
        Colors.orange,
      ));
    }

    // Overdue events insight
    if (widget.controller.overdueEvents > 0) {
      insights.add(_buildInsightCard(
        context,
        'Overdue Events',
        'You have ${widget.controller.overdueEvents} overdue events that need attention.',
        Icons.error,
        Colors.red,
      ));
    }

    // Upcoming events insight
    if (widget.controller.upcomingEvents > 5) {
      insights.add(_buildInsightCard(
        context,
        'Busy Schedule',
        'You have ${widget.controller.upcomingEvents} upcoming events. Stay organized!',
        Icons.schedule,
        AppColors.eventsColor,
      ));
    }

    if (insights.isEmpty) {
      insights.add(_buildInsightCard(
        context,
        'Great Job!',
        'Your event management is on track. Keep up the good work!',
        Icons.check_circle,
        Colors.green,
      ));
    }

    return insights;
  }

  Widget _buildInsightCard(BuildContext context, String title, String description, IconData icon, Color color) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyInsightsCard(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Icon(
            Icons.lightbulb_outline,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No Insights Available',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add events to your schedule to see personalized insights and recommendations.',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildManagementRecommendations(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Management Recommendations',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        _buildRecommendationCard(
          context,
          'Schedule Regular Check-ups',
          'Set up recurring events for routine cattle health checks and vaccinations.',
          Icons.schedule,
          AppColors.eventsColor,
        ),
        const SizedBox(height: 12),
        _buildRecommendationCard(
          context,
          'Use Event Categories',
          'Organize your events by type (health, breeding, feeding) for better tracking.',
          Icons.category,
          Colors.blue,
        ),
      ],
    );
  }

  Widget _buildRecommendationCard(BuildContext context, String title, String description, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
