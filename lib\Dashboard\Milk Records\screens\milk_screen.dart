import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../controllers/milk_controller.dart';
import '../tabs/milk_analytics_tab.dart';
import '../tabs/milk_records_tab.dart';
import '../tabs/milk_sales_tab.dart';
import '../tabs/milk_insights_tab.dart';
import '../dialogs/milk_record_form_dialog.dart';
import '../../../routes/app_routes.dart';
import '../../widgets/index.dart';
import '../../widgets/mixins/screen_state_mapper.dart'; // Import Screen State Mapper
import '../../../constants/app_layout.dart'; // Import Universal Layout
import '../../../constants/app_tabs.dart'; // Import Universal Tabs

/// Milk screen with Provider-managed controller lifecycle
/// Following the CattleScreen pattern: StatelessWidget with ChangeNotifierProvider
class MilkScreen extends StatelessWidget {
  const MilkScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => MilkController(),
      child: const _MilkScreenContent(),
    );
  }
}

/// Internal content widget that accesses the controller via Provider
class _MilkScreenContent extends StatefulWidget {
  const _MilkScreenContent();

  @override
  State<_MilkScreenContent> createState() => _MilkScreenContentState();
}

class _MilkScreenContentState extends State<_MilkScreenContent>
    with TickerProviderStateMixin, UniversalScreenState, ScreenStateMapper {
  late TabController _tabController;
  UniversalTabManager? _tabManager;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return UniversalLayout.tabScreen(
      title: 'Milk Management',
      body: Consumer<MilkController>(
        builder: (context, milkController, child) {
          // Initialize tab manager here where Provider context is available
          _tabManager ??= UniversalTabManager.fourTabs(
            controller: _tabController,
            tabViews: [
              // Use Builder widgets for lazy initialization
              Builder(
                builder: (context) => MilkAnalyticsTab(controller: milkController),
              ),
              Builder(
                builder: (context) => MilkRecordsTab(controller: milkController),
              ),
              Builder(
                builder: (context) => MilkSalesTab(controller: milkController),
              ),
              Builder(
                builder: (context) => MilkInsightsTab(controller: milkController),
              ),
            ],
            labels: const ['Analytics', 'Records', 'Sales', 'Insights'],
            icons: const [Icons.analytics, Icons.list_alt, Icons.point_of_sale, Icons.lightbulb],
            colors: [
              Colors.blue, // Tab 0: Analytics (Blue)
              const Color(0xFF388E3C), // Tab 1: Records (Green)
              Colors.purple, // Tab 2: Sales (Purple)
              Colors.indigo, // Tab 3: Insights (Indigo)
            ],
            showFABs: const [false, true, true, false], // FAB on Records and Sales tabs
            indicatorColor: UniversalEmptyStateTheme.milk,
          );

          return UniversalStateBuilder(
            state: getScreenStateFromController(milkController),
            errorMessage: milkController.errorMessage,
            onRetry: () {}, // No manual retry needed - reactive streams auto-recover
            moduleColor: UniversalEmptyStateTheme.milk,
            loadingWidget: UniversalLoadingIndicator.milk(),
            errorWidget: UniversalErrorIndicator.milk(
              message: milkController.errorMessage ?? 'Failed to load milk data',
              onRetry: () {}, // No manual retry needed - reactive streams auto-recover
            ),
            child: _tabManager!, // Tab manager is guaranteed to be initialized above
          );
        },
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.bar_chart),
          onPressed: () => Navigator.pushNamed(
            context,
            AppRoutes.milkReport,
          ),
          tooltip: 'View Milk Reports',
        ),
        // Removed manual refresh - reactive streams handle all updates automatically
      ],
      floatingActionButton: AnimatedBuilder(
        animation: _tabController,
        builder: (context, child) {
          // Only rebuild FAB when tab changes, not the entire screen
          return _tabManager?.getCurrentFAB(
            onPressed: _showAddMilkDialog,
            tooltip: 'Add Record',
            backgroundColor: UniversalEmptyStateTheme.milk,
          ) ?? const SizedBox.shrink(); // Handle null case
        },
      ), // Optimized FAB management with AnimatedBuilder
      // No onRefresh needed - reactive streams handle all data updates automatically
    );
  }

  void _showAddMilkDialog() {
    final milkController = context.read<MilkController>();

    showDialog(
      context: context,
      builder: (context) => MilkRecordFormDialog(
        cattle: milkController.cattle,
        onSave: (record) async {
          await milkController.addMilkRecord(record);
        },
        // No onRecordAdded callback needed - reactive streams handle updates!
      ),
    );
  }

  // State mapping is now handled by ScreenStateMapper mixin
}