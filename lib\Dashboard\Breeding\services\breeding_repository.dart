
import 'package:isar/isar.dart';
import '../models/breeding_record_isar.dart';
import '../models/pregnancy_record_isar.dart';
import '../../../services/database/isar_service.dart';

/// Pure reactive repository for Breeding module database operations
/// Following the cattle/weight module pattern: Stream-only, logic-free, simple CRUD
/// Maximum purity: no error handling, no logging - exceptions bubble up naturally
class BreedingRepository {
  final IsarService _isarService;

  // Public constructor with explicit dependency injection
  BreedingRepository(this._isarService);

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  //=== REACTIVE BREEDING STREAMS ===//

  /// Watches all breeding records with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<BreedingRecordIsar>> watchAllBreedingRecords() {
    return _isar.breedingRecordIsars.where().watch(fireImmediately: true);
  }

  /// Watches all pregnancy records with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<PregnancyRecordIsar>> watchAllPregnancyRecords() {
    return _isar.pregnancyRecordIsars.where().watch(fireImmediately: true);
  }

  //=== BREEDING RECORDS CRUD ===//

  /// Save (add or update) a breeding record using Isar's native upsert
  Future<void> saveBreedingRecord(BreedingRecordIsar record) async {
    await _isar.writeTxn(() async {
      await _isar.breedingRecordIsars.put(record);
    });
  }

  /// Delete a breeding record by its Isar ID
  Future<void> deleteBreedingRecord(int id) async {
    await _isar.writeTxn(() async {
      await _isar.breedingRecordIsars.delete(id);
    });
  }

  //=== PREGNANCY RECORDS CRUD ===//

  /// Save (add or update) a pregnancy record using Isar's native upsert
  Future<void> savePregnancyRecord(PregnancyRecordIsar record) async {
    await _isar.writeTxn(() async {
      await _isar.pregnancyRecordIsars.put(record);
    });
  }

  /// Delete a pregnancy record by its Isar ID
  Future<void> deletePregnancyRecord(int id) async {
    await _isar.writeTxn(() async {
      await _isar.pregnancyRecordIsars.delete(id);
    });
  }

  //=== QUERY METHODS FOR ANALYTICS ===//

  /// Get breeding records for a specific cattle (for analytics)
  /// Returns a Future<List> for one-time data fetching
  Future<List<BreedingRecordIsar>> getBreedingRecordsForCattle(String cattleId) async {
    return await _isar.breedingRecordIsars
        .filter()
        .cattleIdEqualTo(cattleId)
        .findAll();
  }

  /// Get pregnancy records for a specific cattle (for analytics)
  /// Returns a Future<List> for one-time data fetching
  Future<List<PregnancyRecordIsar>> getPregnancyRecordsForCattle(String cattleId) async {
    return await _isar.pregnancyRecordIsars
        .filter()
        .cattleIdEqualTo(cattleId)
        .findAll();
  }
}